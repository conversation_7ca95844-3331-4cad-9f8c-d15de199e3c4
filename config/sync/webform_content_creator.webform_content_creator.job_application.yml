uuid: dc7d86ce-2036-4675-aa34-b36dc089bf6e
langcode: en
status: true
dependencies: {  }
id: job_application
title: 'Job application'
webform: job_application
content_type: null
field_title: null
use_encrypt: null
encryption_profile: null
redirect_to_entity: false
redirect_to_entity_message: ''
redirect_to_entity_message_on_update: '0'
sync_unique: false
sync_content: false
sync_content_delete: false
sync_content_field: field_webform_submission_id
sync_content_node_field: null
target_bundle: company-job_application
target_entity_type: group_relationship
elements:
  changed:
    type: true
    mapping: default_mapping
    webform_field: changed
    custom_check: 0
    custom_value: ''
  created:
    type: true
    mapping: default_mapping
    webform_field: created
    custom_check: 0
    custom_value: ''
  default_langcode:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '1'
  entity_id:
    type: true
    mapping: default_mapping
    webform_field: uid
    custom_check: 0
    custom_value: ''
  field_certificates:
    type: false
    mapping: default_mapping
    webform_field: certificates_existing
    custom_check: 0
    custom_value: ''
  field_certificates_new:
    type: false
    mapping: default_mapping
    webform_field: certificates_upload
    custom_check: 0
    custom_value: ''
  field_cv:
    type: false
    mapping: default_mapping
    webform_field: cv_existing
    custom_check: 0
    custom_value: ''
  field_cv_new:
    type: false
    mapping: default_mapping
    webform_field: cv_upload
    custom_check: 0
    custom_value: ''
  field_education:
    type: false
    mapping: default_mapping
    webform_field: education
    custom_check: 0
    custom_value: ''
  field_email:
    type: false
    mapping: default_mapping
    webform_field: email
    custom_check: 0
    custom_value: ''
  field_first_name:
    type: false
    mapping: default_mapping
    webform_field: first_name
    custom_check: 0
    custom_value: ''
  field_job_post:
    type: true
    mapping: default_mapping
    webform_field: entity_id
    custom_check: 0
    custom_value: ''
  field_last_name:
    type: false
    mapping: default_mapping
    webform_field: last_name
    custom_check: 0
    custom_value: ''
  field_linkedin_profile:
    type: false
    mapping: default_mapping
    webform_field: linkedin
    custom_check: 0
    custom_value: ''
  field_message:
    type: false
    mapping: default_mapping
    webform_field: message
    custom_check: 0
    custom_value: ''
  field_phone_number:
    type: false
    mapping: default_mapping
    webform_field: phone_number
    custom_check: 0
    custom_value: ''
  field_reference_number:
    type: false
    mapping: default_mapping
    webform_field: reference_number
    custom_check: 0
    custom_value: ''
  field_webform_submission_id:
    type: true
    mapping: default_mapping
    webform_field: sid
    custom_check: 0
    custom_value: ''
  gid:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: '[webform_submission:source-entity-group-id]'
  group_type:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: company
  langcode:
    type: true
    mapping: default_mapping
    webform_field: langcode
    custom_check: 0
    custom_value: ''
  plugin_id:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: job_application
  type:
    type: false
    mapping: default_mapping
    webform_field: ''
    custom_check: '1'
    custom_value: company-job_application
  uid:
    type: true
    mapping: default_mapping
    webform_field: uid
    custom_check: 0
    custom_value: ''
