<?php

/**
 * @file
 * BWY access control module.
 */

use Drupal\Core\Session\AccountInterface;

/**
 * Implements hook_file_download().
 */
function bwy_access_file_download($uri) {
  // Only handle private files in talent-cv and talent-certificates directories.
  if (!preg_match('#^private://(talent-cv|talent-certificates)/#', $uri)) {
    return NULL;
  }

  /** @var \Drupal\file\Entity\File[] $files */
  $files = \Drupal::entityTypeManager()
    ->getStorage('file')
    ->loadByProperties(['uri' => $uri]);

  if (empty($files)) {
    // Deny access if file not found.
    return -1;
  }

  $file = reset($files);
  $file_id = $file->id();
  $current_user = \Drupal::currentUser();

  // Check if this file is used in job application.
  $job_app_access = _bwy_access_check_job_application_file_access($file_id, $current_user);
  if ($job_app_access !== NULL) {
    return $job_app_access;
  }

  // Check if this file is used in talent profile.
  $profile_access = _bwy_access_check_talent_profile_file_access($file_id, $current_user);
  if ($profile_access !== NULL) {
    return $profile_access;
  }

  // Deny access by default.
  return -1;
}

/**
 * Check access for talent profile files (CV and certificates).
 *
 * @param int $file_id
 *   The file ID.
 * @param \Drupal\Core\Session\AccountInterface $current_user
 *   The current user.
 *
 * @return array|int|null
 *   Access headers array, -1 for deny, or NULL if not applicable.
 */
function _bwy_access_check_talent_profile_file_access($file_id, AccountInterface $current_user) {
  $database = \Drupal::database();

  // Check CV field.
  $cv_query = $database->select('profile__field_cv', 'cv')
    ->fields('cv', ['entity_id'])
    ->condition('field_cv_target_id', $file_id);
  $profile_id = $cv_query->execute()->fetchField();

  // Check certificates field if not found in CV.
  if (!$profile_id) {
    $cert_query = $database->select('profile__field_certificates', 'cert')
      ->fields('cert', ['entity_id'])
      ->condition('field_certificates_target_id', $file_id);
    $profile_id = $cert_query->execute()->fetchField();
  }

  if (!$profile_id) {
    // File not used in talent profile.
    return NULL;
  }

  /** @var \Drupal\profile\Entity\ProfileInterface $profile */
  $profile = \Drupal::entityTypeManager()
    ->getStorage('profile')
    ->loadByProperties([
      'profile_id' => $profile_id,
      'type' => 'talent',
    ]);
  $profile = reset($profile);

  if (!$profile) {
    return -1;
  }

  // Allow profile owner to access their own files.
  // Allow administrators.
  // Allow company recruiters if profile consent is given.
  if (
    ($profile->getOwnerId() == $current_user->id())
    || in_array('administrator', $current_user->getRoles())
    || (in_array('company_recruiter', $current_user->getRoles()) && $profile->get('field_talent_profile_consent')->value)
  ) {
    return _bwy_access_get_file_headers($file_id);
  }

  // Deny access.
  return -1;
}

/**
 * Check access for job application files.
 *
 * @param int $file_id
 *   The file ID.
 * @param \Drupal\Core\Session\AccountInterface $current_user
 *   The current user.
 *
 * @return array|int|null
 *   Access headers array, -1 for deny, or NULL if not applicable.
 */
function _bwy_access_check_job_application_file_access($file_id, AccountInterface $current_user) {
  $database = \Drupal::database();

  // Check all job application CV fields.
  $fields_to_check = [
    'group_relationship__field_cv',
    'group_relationship__field_cv_new',
    'group_relationship__field_certificates',
    'group_relationship__field_certificates_new',
  ];

  $job_application_id = NULL;
  foreach ($fields_to_check as $table) {
    $field_name = str_replace('group_relationship__', '', $table) . '_target_id';

    $query = $database->select($table, 't')
      ->fields('t', ['entity_id'])
      ->condition($field_name, $file_id);

    $result = $query->execute()->fetchField();
    if ($result) {
      $job_application_id = $result;
      break;
    }
  }

  if (!$job_application_id) {
    // File not used in job application.
    return NULL;
  }

  /** @var \Drupal\group\Entity\GroupRelationship $job_application */
  $job_application = \Drupal::entityTypeManager()
    ->getStorage('group_relationship')
    ->loadByProperties([
      'id' => $job_application_id,
      'type' => 'company-job_application',
    ]);
  $job_application = reset($job_application);

  if (!$job_application) {
    return -1;
  }

  // Allow the applicant to access their own files.
  // Allow administrators.
  // Allow company recruiters if they are members of the company group.
  if (
    ($applicant_id = $job_application->getEntity()->id()) && $applicant_id == $current_user->id()
    || in_array('administrator', $current_user->getRoles())
    || (
      in_array('company_recruiter', $current_user->getRoles())
      && $job_application->getGroup()->getMember($current_user)
    )
  ) {
    return _bwy_access_get_file_headers($file_id);
  }

  // Deny access.
  return -1;
}

/**
 * Get file headers for download.
 *
 * @param int $file_id
 *   The file ID.
 *
 * @return array
 *   HTTP headers for file download.
 */
function _bwy_access_get_file_headers($file_id) {
  /** @var \Drupal\file\Entity\File $file */
  $file = \Drupal::entityTypeManager()
    ->getStorage('file')
    ->load($file_id);

  if (!$file) {
    return -1;
  }

  return [
    'Content-Type' => $file->getMimeType(),
    'Content-Length' => $file->getSize(),
    'Content-Disposition' => 'attachment; filename="' . $file->getFilename() . '"',
  ];
}
