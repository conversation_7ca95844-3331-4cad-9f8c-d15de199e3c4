<?php

/**
 * @file
 * Job application module.
 */

use <PERSON><PERSON>al\Core\Block\BlockPluginInterface;
use <PERSON><PERSON>al\Core\Entity\Display\EntityViewDisplayInterface;
use <PERSON><PERSON>al\Core\Entity\EntityInterface;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Render\Element;
use Drupal\Core\StringTranslation\TranslatableMarkup;
use Drupal\Core\Url;
use <PERSON><PERSON>al\node\Entity\Node;
use Drupal\node\NodeInterface;
use Drupal\search_api\Query\QueryInterface;
use Drupal\views\ViewExecutable;
use Drupal\webform\Entity\Webform;
use Drupal\webform\WebformSubmissionInterface;

/**
 * Implements hook_token_info().
 *
 * Defines custom tokens for job application module.
 */
function bwy_job_application_token_info() {
  $info = [];

  // Add tokens to node type.
  $info['tokens']['node']['group_id'] = [
    'name' => 'Group ID',
    'description' => 'The ID of the company group this node belongs to.',
  ];

  // Add tokens to webform submission source entity.
  $info['tokens']['webform_submission']['source-entity-group-id'] = [
    'name' => 'Source Entity Group ID',
    'description' => 'The ID of the company group the source entity belongs to.',
  ];

  return $info;
}

/**
 * Implements hook_tokens().
 *
 * Provides values for custom tokens.
 */
function bwy_job_application_tokens($type, $tokens, array $data, array $options, $bubbleable_metadata) {
  $replacements = [];

  // Handle node tokens.
  if ($type == 'node' && !empty($data['node']) && !empty($tokens['group_id'])) {
    $node = $data['node'];
    $replacements[$tokens['group_id']] = _bwy_job_application_get_group_id_for_node($node);
  }

  // Handle webform_submission tokens.
  if ($type == 'webform_submission' && !empty($data['webform_submission'])) {
    /** @var \Drupal\webform\WebformSubmissionInterface $submission */
    $submission = $data['webform_submission'];

    foreach ($tokens as $name => $original) {
      switch ($name) {
        case 'source-entity-group-id':
          $source_entity = $submission->getSourceEntity();
          if ($source_entity instanceof NodeInterface) {
            $replacements[$original] = _bwy_job_application_get_group_id_for_node($source_entity);
          }
          break;
      }
    }
  }

  return $replacements;
}

/**
 * Implements hook_form_alter().
 */
function bwy_job_application_form_alter(array &$form, FormStateInterface $form_state, string $form_id): void {
  if ($form_id === 'private_message_add_form') {
    $form['message']['widget'][0]['#title_display'] = 'invisible';
    $form['message']['widget'][0]['#placeholder'] = t('Write a message...');

    $form['field_file']['widget'][0]['#title_display'] = 'invisible';
    $form['field_file']['widget'][0]['#description'] = '';

    // Remove the help text from the textarea.
    $form['message']['widget']['#after_build'][] = '_bwy_job_application_remove_textarea_help';

    // Check if we're creating a new thread (not replying to an existing one).
    $thread = $form_state->get('thread');

    if ($thread) {
      $job_id = $thread->field_job->target_id;
      $job = \Drupal::entityTypeManager()
        ->getStorage('node')
        ->load($job_id);

      if (!$job->status->value) {
        // Hide all form fields.
        foreach (Element::children($form) as $key) {
          if (!in_array($key, ['form_build_id', 'form_token', 'form_id'])) {
            $form[$key]['#access'] = FALSE;
          }
        }

        // Add message markup.
        $form['job_unpublished_message'] = [
          '#type' => 'markup',
          '#markup' => t('This job posting is no longer available.'),
        ];
      }

      return;
    }

    // Hide the members field if it exists.
    if (isset($form['members'])) {
      $form['members']['#access'] = FALSE;
    }

    // Get the job ID from the GET parameter.
    $request = \Drupal::service('request_stack')->getCurrentRequest();
    $job_id = $request->query->get('job');

    // Add a job post reference field to the form.
    $form['job_post_reference'] = [
      '#type' => 'entity_autocomplete',
      '#title' => t('Job Post'),
      '#target_type' => 'node',
      '#selection_settings' => [
        'target_bundles' => ['job_post'],
      ],
      '#required' => TRUE,
      '#access' => FALSE,
    ];

    // Set default value if job parameter exists.
    if ($job_id) {
      $form['job_post_reference']['#default_value'] = \Drupal::entityTypeManager()
        ->getStorage('node')
        ->load($job_id);
    }

    // Add a custom submit handler to save the job post reference to the thread.
    $form['actions']['submit']['#submit'][] = 'bwy_job_application_private_message_form_submit';
  }

  if (in_array($form_id, ['node_job_post_edit_form', 'node_job_post_form'])) {
    if (isset($form['entitygroupfield']['widget'][0]['top']['links'])) {
      $form['entitygroupfield']['widget'][0]['top']['links']['#access'] = FALSE;
    }

    if (isset($form['unpublish_state']['widget'][0])) {
      // Set default value to 'expired'.
      $form['unpublish_state']['widget'][0]['#default_value'] = ['expired'];

      // Hide the select element.
      $form['unpublish_state']['widget'][0]['#access'] = FALSE;
    }

    if (isset($form['unpublish_on']['widget'][0]['value'])) {
      // Change label of "Unpublish on" field.
      $form['unpublish_on']['widget'][0]['value']['#title'] = new TranslatableMarkup('Valid until');
    }

    if (isset($form['revision'])) {
      $form['revision']['#access'] = FALSE;
    }

    if (isset($form['revision_log'])) {
      $form['revision_log']['#access'] = FALSE;
    }

    if (isset($form['revision_information'])) {
      $form['revision_information']['#access'] = FALSE;
    }
  }

  if (str_starts_with($form_id, 'webform_submission_job_application_')) {
    _bwy_job_application_nested_elements($form['elements']);
  }

}

/**
 * Recursively search for elements in form structure.
 *
 * @param array &$elements
 *   The form elements array to search through.
 */
function _bwy_job_application_nested_elements(array &$elements) {
  foreach (Element::children($elements) as $key) {
    if ($key === 'cv_existing') {
      $elements[$key]['#options']['upload'] = new TranslatableMarkup('Upload another CV');
    }
    elseif (isset($elements[$key]) && is_array($elements[$key])) {
      // Recursively search nested elements.
      _bwy_job_application_nested_elements($elements[$key]);
    }
  }
}

/**
 * Implements hook_node_view().
 */
function bwy_job_application_node_view(array &$build, NodeInterface $node, EntityViewDisplayInterface $display, $view_mode) {
  if ($node->bundle() !== 'job_post' || $view_mode !== 'full') {
    return;
  }

  $current_user = \Drupal::currentUser();
  if (!$current_user->hasPermission('use private messaging system')) {
    return;
  }

  $author_id = $node->getOwnerId();
  if ($current_user->id() === $author_id) {
    return;
  }

  $author = \Drupal::entityTypeManager()->getStorage('user')->load($author_id);
  if (!$author) {
    return;
  }

  $profile_storage = \Drupal::entityTypeManager()->getStorage('profile');
  $profiles = $profile_storage->loadByProperties([
    'uid' => $current_user->id(),
    'type' => 'talent',
  ]);
  $profile = !empty($profiles) ? reset($profiles) : NULL;

  if (!$profile || !$profile->field_talent_profile_consent->value) {
    return;
  }

  $current_user_entity = \Drupal::entityTypeManager()->getStorage('user')->load($current_user->id());
  $members = [$current_user_entity, $author];
  $private_message_mapper = \Drupal::service('private_message.mapper');
  $thread_id = $private_message_mapper->getThreadIdForMembers($members);

  $new_job_message = FALSE;
  $url = NULL;

  if ($thread_id) {
    $thread = \Drupal::entityTypeManager()
      ->getStorage('private_message_thread')
      ->load($thread_id);

    if ($thread) {
      $current_job_id = $thread->field_job->target_id;
      if ($current_job_id != $node->id()) {
        $new_job_message = TRUE;
      }

      $url_options = [
        'attributes' => [
          'class' => array_merge(['private_message_link'], bwy_job_application_secondary_button_classes()),
          'role' => 'button',
        ],
      ];

      if ($new_job_message) {
        $url_options['query'] = [
          'thread' => $thread->id(),
          'job' => $node->id(),
        ];
      }

      $url = Url::fromRoute('entity.private_message_thread.canonical', [
        'private_message_thread' => $thread->id(),
      ], $url_options);
    }
  }

  if (!$url) {
    $new_job_message = TRUE;
    $url = Url::fromRoute('bwy_job_application.create_message', [
      'recipient' => $author->id(),
      'job' => $node->id(),
    ], [
      'attributes' => [
        'class' => array_merge(['private_message_link'], bwy_job_application_secondary_button_classes()),
        'role' => 'button',
      ],
    ]);
  }

  $build['private_message_link'] = [
    '#type' => 'link',
    '#url' => $url,
    '#title' => t('Send message'),
    '#prefix' => '<div class="private_message_link_wrapper">',
    '#suffix' => '</div>',
    '#weight' => 100,
    '#cache' => [
      'contexts' => ['user'],
      'tags' => $current_user_entity->getCacheTags(),
      'max-age' => 0,
    ],
    '#attributes' => $new_job_message ? [
      'class' => array_merge(['confirm-profile-share'], bwy_job_application_secondary_button_classes()),
      'role' => 'button',
    ] : [],
    '#attached' => [
      'library' => [
        'bwy_job_application/bwy_job_application',
      ],
    ],
  ];
}

/**
 * Custom submit handler for the private message form.
 *
 * @param array $form
 *   The form array.
 * @param \Drupal\Core\Form\FormStateInterface $form_state
 *   The form state object.
 */
function bwy_job_application_private_message_form_submit(array $form, FormStateInterface $form_state): void {
  // Get the job post reference value.
  $job_post_id = $form_state->getValue('job_post_reference');

  // Get the private message thread entity.
  $thread = $form_state->get('private_message_thread');

  if ($thread && $job_post_id) {
    // Set the field value.
    $thread->set('field_job', $job_post_id);
    $thread->save();
  }
}

/**
 * Remove the help text from the textarea.
 */
function _bwy_job_application_remove_textarea_help($form_element, FormStateInterface $form_state) {
  if (isset($form_element[0]['format'])) {
    unset($form_element[0]['format']['help']);
    unset($form_element[0]['format']['guidelines']);
  }

  return $form_element;
}

/**
 * Implements hook_preprocess_block().
 */
function bwy_job_application_preprocess_block(&$variables) {
  if (
    $variables['elements']['#base_plugin_id'] !== 'webform_block'
    || $variables['elements']['#configuration']['webform_id'] !== 'job_application'
  ) {
    return;
  }

  $account = \Drupal::currentUser();
  if (!$account->isAuthenticated()) {
    return;
  }

  //$a = _bwy_job_application_get_group_id_for_node($variables['elements']['#configuration']['node']);

  $submission_exists = \Drupal::entityTypeManager()
    ->getStorage('group_relationship')
    ->getQuery()
    ->condition('type', 'company-job_application')
    ->condition('gid', 1)
    ->condition('uid', $account->id())
    ->range(0, 1)
    ->accessCheck(FALSE)
    ->execute();

  if (empty($submission_exists)) {
    return;
  }

  $webform = Webform::load('job_application');
  $closed_message = $webform->getSetting('previous_submission_message')
    ?: t("You've already applied to this position");

  $variables['content'] = [
    '#markup' => '<div class="webform-closed-message">' . $closed_message . '</div>',
    '#cache' => [
      'contexts' => ['user'],
      'tags' => ['webform_submission_list'],
      'max-age' => 0,
    ],
  ];
}

/**
 * Implements hook_search_api_query_alter().
 */
function bwy_job_application_search_api_query_alter(QueryInterface $query) {
  if ($query->getIndex()->id() !== 'talents') {
    return;
  }

  $route_match = \Drupal::service('current_route_match');
  $node_id = $route_match->getParameter('node');

  if (!$node_id) {
    return;
  }

  $node = \Drupal::entityTypeManager()
    ->getStorage('node')
    ->load($node_id);

  if (!$node instanceof NodeInterface) {
    return;
  }

  $term_ids = [];

  if ($node->hasField('field_place_of_work') && !$node->get('field_place_of_work')->isEmpty()) {
    $place_term = $node->get('field_place_of_work')->entity;
    if ($place_term) {
      $term_ids['city_tid'][] = $place_term->id();
      $children = \Drupal::entityTypeManager()->getStorage('taxonomy_term')
        ->loadByProperties(['parent' => $place_term->id()]);
      foreach ($children as $child) {
        $term_ids['city_tid'][] = $child->id();
      }
    }
  }

  if ($node->hasField('field_education') && !$node->get('field_education')->isEmpty()) {
    $education_term = $node->get('field_education')->entity;
    if ($education_term) {
      $query_ids = \Drupal::entityQuery('taxonomy_term')
        ->accessCheck(FALSE)
        ->condition('vid', $education_term->bundle())
        ->condition('weight', $education_term->getWeight(), '<=')
        ->execute();
      $term_ids['education_tid'] = array_values($query_ids);
    }
  }

  if ($node->hasField('field_professional_field') && !$node->get('field_professional_field')->isEmpty()) {
    $term_ids['professional_field_tid'][] = $node->get('field_professional_field')->target_id;
  }

  if ($node->hasField('field_languages') && !$node->get('field_languages')->isEmpty()) {
    foreach ($node->get('field_languages') as $lang_item) {
      $term_ids['languages_tid'][] = $lang_item->target_id;
    }
  }

  // Apply filters to the Solr query.
  foreach ($term_ids as $field => $ids) {
    if (!empty($ids)) {
      // Use the exact field names from the index.
      $query->addCondition($field, $ids, 'IN');
    }
  }
}

/**
 * Implements hook_entity_delete().
 */
function bwy_job_application_entity_delete(EntityInterface $entity) {
  if (!$entity instanceof WebformSubmissionInterface) {
    return;
  }

  \Drupal::service('cache_tags.invalidator')->invalidateTags(['webform_submission_list']);
}

/**
 * Helper function to get the group ID for a node.
 *
 * @param \Drupal\node\NodeInterface $node
 *   The node to get the group ID for.
 *
 * @return string|null
 *   The group ID or NULL if not found.
 */
function _bwy_job_application_get_group_id_for_node(NodeInterface $node) {
  // Use the entity type manager to find the group this node belongs to.
  $group_relation_storage = \Drupal::entityTypeManager()->getStorage('group_relationship');
  $group_relations = $group_relation_storage->loadByProperties([
    'entity_id' => $node->id(),
    'plugin_id' => 'group_node:job_post',
  ]);

  $group_id = NULL;
  if (!empty($group_relations)) {
    /** @var \Drupal\group\Entity\GroupRelationship $group_relation */
    $group_relation = reset($group_relations);
    $group_id = $group_relation->getGroup()->id();
  }

  return $group_id;
}

/**
 * Returns the secondary button classes.
 *
 * @return array
 *   An array of CSS classes for secondary buttons.
 */
function bwy_job_application_secondary_button_classes() {
  return [
    'body-3',
    'inline-flex',
    'items-center',
    'justify-center',
    'gap-2',
    'rounded-full-99',
    'transition-colors',
    'focus:outline-none',
    'focus:ring-2',
    'bg-white',
    'text-blue-600',
    'border-2',
    'border-blue-600',
    'hover:bg-blue-500',
    'focus:ring-blue-500',
    'px-6',
    'py-2.5',
    'w-full',
  ];
}

/**
 * Implements hook_block_view_alter().
 */
function bwy_job_application_block_view_alter(array &$build, BlockPluginInterface $block) {
  if ($block->getBaseId() !== 'private_message_inbox_block') {
    return;
  }

  $build['#pre_render'][] = function ($build) {
    if (!empty($build['content']['no_threads'])) {
      unset($build['content']['no_threads']);
    }
    return $build;
  };
}

/**
 * Implements hook_views_pre_view().
 */
function bwy_job_application_views_pre_view(ViewExecutable $view, $display_id, array &$args) {
  if ($view->id() !== 'job_talents' || $display_id !== 'talents') {
    return;
  }

  $route_match = \Drupal::service('current_route_match');
  $node_id = $route_match->getParameter('node');

  if (!$node_id) {
    return;
  }

  $node = \Drupal::entityTypeManager()
    ->getStorage('node')
    ->load($node_id);

  if (!$node instanceof NodeInterface) {
    return;
  }

  // Create the header content.
  $header_content = '<div class="talents-view-header">';

  if ($node->hasField('title') && !$node->get('title')->isEmpty()) {
    $header_content .= '<h2 class="job-title">' . $node->get('title')->value . '</h2>';
  }

  $header_content .= t('The list of potential candidates represents talent that meets the following criteria set out in the terms of the current advertisement:');

  if ($node->hasField('field_professional_field') && !$node->get('field_professional_field')->isEmpty()) {
    $professional_field = $node->get('field_professional_field')->entity;
    if ($professional_field) {
      $header_content .= '<div class="job-professional-field">' . t('Professional field: @field', ['@field' => $professional_field->label()]) . '</div>';
    }
  }

  if ($node->hasField('field_education') && !$node->get('field_education')->isEmpty()) {
    $education = $node->get('field_education')->entity;
    if ($education) {
      $header_content .= '<div class="job-education">' . t('Minimum education required: @education', ['@education' => $education->label()]) . '</div>';
    }
  }

  if ($node->hasField('field_place_of_work') && !$node->get('field_place_of_work')->isEmpty()) {
    $place = $node->get('field_place_of_work')->entity;
    if ($place) {
      $header_content .= '<div class="job-location">' . t('Place of work: @place', ['@place' => $place->label()]) . '</div>';
    }
  }
  if ($node->hasField('field_languages') && !$node->get('field_languages')->isEmpty()) {
    /** @var \Drupal\Core\Field\EntityReferenceFieldItemListInterface $reference_field */
    $reference_field = $node->get('field_languages');
    $languages = $reference_field->referencedEntities();
    if (!empty($languages)) {
      $language_labels = [];
      foreach ($languages as $language) {
        $language_labels[] = $language->label();
      }
      $header_content .= '<div class="job-languages">' . t('Required languages: @languages', ['@languages' => implode(', ', $language_labels)]) . '</div>';
    }
  }

  $header_content .= '</div>';

  // Add the header using setHandler.
  $options = [
    'id' => 'area_text_custom',
    'table' => 'views',
    'field' => 'area_text_custom',
    'relationship' => 'none',
    'group_type' => 'none',
    'admin_label' => '',
    'empty' => TRUE,
    'tokenize' => FALSE,
    'content' => $header_content,
    'plugin_id' => 'text_custom',
  ];

  $view->setHandler('talents', 'header', 'area_text_custom', $options);
}

/**
 * Implements hook_menu_local_tasks_alter().
 */
function bwy_job_application_menu_local_tasks_alter(array &$data, $route_name, $cacheability) {
  $target_route_name = 'view.job_talents.talents';

  // This is the exact key seen in the array dump.
  $tab_key = 'views_view:' . $target_route_name;

  if (isset($data['tabs'][0][$tab_key])) {
    $node_param = \Drupal::routeMatch()->getParameter('node');

    if ($node_param instanceof NodeInterface) {
      $node = $node_param;
    }
    elseif (is_numeric($node_param)) {
      $node = Node::load($node_param);
    }
    else {
      $node = NULL;
    }

    if (!$node || $node->bundle() !== 'job_post') {
      unset($data['tabs'][0][$tab_key]);
    }
  }
}
